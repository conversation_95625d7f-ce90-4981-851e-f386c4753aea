# 批量分析頁面圖片下載功能

## 功能概述

為 `BatchAnalysisPage.vue` 添加了圖片下載功能，讓使用者除了可以選擇 Excel 下載，也可以選擇圖片下載。圖片的資料格式與 Excel 一樣，並且圖片是由原本 Excel 所有分頁由上至下拼貼成一張大圖。

## 主要修改

### 1. UI 界面修改

在結果顯示區域添加了下載格式選擇器：

```vue
<!-- 下載格式選擇 -->
<div class="row items-center justify-end q-mb-sm">
  <div class="text-subtitle2 q-mr-md">下載格式：</div>
  <q-btn-toggle
    v-model="outputFormat"
    :options="[
      { label: 'Excel', value: 'excel' },
      { label: '圖片', value: 'image' }
    ]"
    color="primary"
    toggle-color="primary"
    unelevated
    dense
  />
</div>
```

### 2. 下載邏輯修改

修改了 `downloadResults` 函數，根據選擇的格式調用不同的下載函數：

```typescript
const downloadResults = () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    if (outputFormat.value === 'excel') {
      downloadExcel();
    } else {
      downloadImage();
    }
  } catch (error) {
    console.error('下載失敗:', error);
    Notify.create({
      type: 'negative',
      message: '檔案下載失敗'
    });
  }
};
```

### 3. 圖片下載功能實現

#### 核心函數

- `downloadImage()`: 主要的圖片下載函數
- `calculateCanvasSize()`: 計算畫布尺寸和準備工作表數據
- `drawSheet()`: 繪製單個工作表到畫布
- `createSheetData()`: 創建工作表數據結構

#### 數據創建函數

為不同的分析類型創建對應的數據：

- `createParametersData()`: 創建參數設定數據
- `createPredictNumbersData()`: 創建預測號碼數據
- `createNonAppearedByFrequencyData()`: 創建未出現號碼數據（依預測次數排序）
- `createNonAppearedBySizeData()`: 創建未出現號碼數據（依大小排序）
- `createTailNumbersData()`: 創建尾數統計數據
- `createActualNumbersData()`: 創建實際開獎號碼數據
- `createTailAppearancesData()`: 創建尾數分析數據

#### 綜合分析專用函數

- `createPatternRdTailAnalysisData()`: 版路分析尾數數據
- `createPatternTailRdAnalysisData()`: 尾數分析尾數數據
- `createPatternComprehensiveAnalysisData()`: 綜合分析尾數數據
- `createPatternRdTailComparisonData()`: 版路+尾數比對數據
- `createPatternRdComprehensiveComparisonData()`: 版路+綜合比對數據
- `createPatternTailComprehensiveComparisonData()`: 尾數+綜合比對數據

## 技術特點

### 1. Canvas 繪圖

使用 HTML5 Canvas API 來繪製圖片：
- 支援中文字體渲染
- 自動計算畫布尺寸
- 支援紅色標記匹配號碼（★符號）

### 2. 數據格式一致性

圖片中的數據格式與 Excel 完全一致：
- 相同的表頭結構
- 相同的數據排列方式
- 相同的匹配標記（★符號）
- 相同的分頁順序

### 3. 分析類型支援

支援所有三種分析類型：
- **版路分析**: 包含預測號碼、未出現號碼、尾數統計等
- **尾數分析**: 包含預測尾數統計
- **綜合分析**: 包含所有版路和尾數分析的子表，以及比對結果

### 4. 自適應佈局

- 自動計算最佳畫布寺寸
- 根據數據量調整行高
- 支援不同彩種的號碼格式

## 使用方式

1. 完成批量分析後，在結果顯示區域選擇下載格式
2. 選擇「圖片」格式
3. 點擊「下載圖片檔案」按鈕
4. 系統會生成一張包含所有分析結果的 PNG 圖片
5. 圖片檔案名稱格式：`{分析方法}_{彩種}_{日期}.png`

## 檔案結構

圖片由上至下包含以下內容（根據分析類型而定）：

### 版路分析
1. 分析參數
2. 預測號碼
3. 未出現號碼-預測次數
4. 未出現號碼-大小排序
5. 尾數統計
6. 實際開獎號碼

### 尾數分析
1. 分析參數
2. 預測尾數
3. 實際開獎號碼

### 綜合分析
1. 分析參數
2. 版路分析尾數
3. 尾數分析尾數
4. 綜合分析尾數
5. 版路+尾數比對
6. 版路+綜合比對
7. 尾數+綜合比對
8. 版路分析-預測號碼
9. 版路分析-未出現號碼-預測次數
10. 版路分析-未出現號碼-大小排序
11. 版路分析-尾數統計
12. 尾數分析-預測尾數
13. 實際開獎號碼

## 注意事項

1. 圖片生成需要瀏覽器支援 Canvas API
2. 大量數據可能會產生較大的圖片檔案
3. 匹配的號碼會以紅色字體和★符號標記
4. 圖片格式為 PNG，確保最佳品質
